# app/api/entities/user.rb
module Entities
    class User < Grape::Entity
      include Rails.application.routes.url_helpers

      # Basic attributes
      expose :id
      expose :role
      expose :verified
      expose :username

      # Avatar URL
      expose :avatar do |user, options|
        # Make sure you’ve set default_url_options[:host] in your environment configs
        user.avatar.url
      end

      # Conditional email (only if matches the supplied :email option)
      expose :email, if: ->(user, options) {
        options[:email].present? && user.email == options[:email]
      }

      # Conditional token (if you passed :token or it’s the current user)
      expose :token, if: ->(user, options) {
        options[:token].present? ||
          (options[:scope].signed_in? && options[:scope].current_user.id == user.id)
      } do |user, options|
        options[:token] || user.generate_jwt
      end

      # Counts
      expose :perks do |user, _|
        user.user_perks.count
      end

      expose :trails do |user, _|
        user.rewards.count
      end

      expose :checkins do |user, _|
        user.checkins.count
      end

      expose :rewards do |user, _|
        user.rewards.count
      end

      # Boolean from instance method
      expose :is_following_facility, as: :is_following_facility do |user, _|
        user.isFollowingAFacility
      end

      # Optional association
      expose :team, using: Entities::Team, if: ->(user, _) {
        user.isVendor || user.isAdmin
      }
    end
end
